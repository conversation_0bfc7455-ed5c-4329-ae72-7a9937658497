import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageCircle, Lightbulb, HelpCircle, FileText, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { getSelectedText, clearSelection, generateSmartPrompts, SmartPrompt } from '@/utils/textSelection';

interface SelectionActionButtonProps {
  onSendMessage: (message: string) => void;
}

const SelectionActionButton: React.FC<SelectionActionButtonProps> = ({ onSendMessage }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showPrompts, setShowPrompts] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [selectedText, setSelectedText] = useState('');
  const [smartPrompts, setSmartPrompts] = useState<SmartPrompt[]>([]);

  useEffect(() => {
    const handleSelectionChange = () => {
      const selection = getSelectedText();

      if (selection && selection.text.length > 0) {
        // Check if the selection is within an assistant message (not user message)
        const element = selection.element;
        const isInAssistantMessage = element?.closest('.chat-bubble-assistant');
        const isInUserMessage = element?.closest('.chat-bubble-user');



        // Only show for assistant messages, not user messages
        if (isInAssistantMessage && !isInUserMessage) {
          setSelectedText(selection.text);
          setSmartPrompts(generateSmartPrompts(selection.text));

          // Calculate position for the button
          const range = selection.range;
          const rect = range.getBoundingClientRect();

          setPosition({
            x: rect.left + rect.width / 2,
            y: rect.top - 60 // Position above the selection
          });

          setIsVisible(true);
          setShowPrompts(false);
        } else {
          setIsVisible(false);
          setSelectedText('');
          setShowPrompts(false);
        }
      } else {
        setIsVisible(false);
        setSelectedText('');
      }
    };

    const handleMouseUp = () => {
      // Small delay to ensure selection is complete
      setTimeout(handleSelectionChange, 10);
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        clearSelection();
        setIsVisible(false);
      }
    };

    document.addEventListener('selectionchange', handleSelectionChange);
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, []);

  const handleShowPrompts = () => {
    setShowPrompts(true);
  };

  const handlePromptSelect = (prompt: SmartPrompt) => {
    onSendMessage(prompt.text);
    clearSelection();
    setIsVisible(false);
    setShowPrompts(false);
  };

  const getIconForPrompt = (promptId: string) => {
    switch (promptId) {
      case 'explain':
        return <Lightbulb className="w-3 h-3" />;
      case 'clarify':
        return <HelpCircle className="w-3 h-3" />;
      case 'examples':
        return <FileText className="w-3 h-3" />;
      case 'implications':
        return <Search className="w-3 h-3" />;
      default:
        return <MessageCircle className="w-3 h-3" />;
    }
  };



  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: 10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 10 }}
          transition={{ duration: 0.2 }}
          className="fixed z-50 selection-action-button"
          style={{
            left: `${position.x}px`,
            top: `${position.y}px`,
            transform: 'translateX(-50%)'
          }}
        >
          {!showPrompts ? (
            <Button
              size="sm"
              onClick={handleShowPrompts}
              className="bg-gray-500 hover:bg-gray-600 text-white text-xs px-3 py-1 h-8 shadow-lg"
            >
              <MessageCircle className="w-3 h-3 mr-1" />
              Ask Proxima
            </Button>
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 p-2 min-w-[250px]"
            >
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-2 px-2">
                Ask about: "{selectedText.length > 30 ? selectedText.substring(0, 30) + '...' : selectedText}"
              </div>
              <div className="space-y-1">
                {smartPrompts.map((prompt) => (
                  <button
                    key={prompt.id}
                    onClick={() => handlePromptSelect(prompt)}
                    className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center gap-2"
                  >
                    {getIconForPrompt(prompt.id)}
                    <span className="flex-1">{prompt.description}</span>
                  </button>
                ))}
              </div>
            </motion.div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SelectionActionButton; 