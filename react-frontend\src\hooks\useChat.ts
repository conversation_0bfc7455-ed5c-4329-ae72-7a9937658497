import { useState, useEffect, useCallback } from 'react';
import { Message, ChatSession, FileAttachment } from '@/types';
import { chatAPI } from "@/utils/api";
import { fileAPI } from "@/utils/api";

export const useChat = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [attachedFiles, setAttachedFiles] = useState<FileAttachment[]>([]);

  // Remove localStorage chat history loading on mount
  useEffect(() => {
    // On mount, do not load chat history from localStorage
    setChatSessions([]);
    setCurrentSessionId(null);
    setMessages([]);
  }, []);

  // Expose a function to fetch and populate chat sessions from backend
  const loadUserChatHistory = async () => {
    setIsLoading(true);
    try {
      const history = await chatAPI.getChatHistory();
      // Assume history is an array of chat sessions or messages
      // You may need to adapt this mapping based on backend response
      if (Array.isArray(history)) {
        // Group messages by session if needed, or treat as one session
        // For now, treat as a single session
        const sessionId = 'default';
        const session: ChatSession = {
          id: sessionId,
          title: 'My Chat History',
          messages: history.map((msg: any, idx: number) => ({
            id: String(msg.id || idx),
            content: msg.user_query || msg.content || '',
            isUser: true, // You may need to distinguish user/assistant
            timestamp: new Date(msg.timestamp || Date.now()),
          })),
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        setChatSessions([session]);
        setCurrentSessionId(sessionId);
        setMessages(session.messages);
      } else {
        setChatSessions([]);
        setCurrentSessionId(null);
        setMessages([]);
      }
    } catch (e) {
      setChatSessions([]);
      setCurrentSessionId(null);
      setMessages([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Expose the loader globally for use in auth hook
  if (typeof window !== 'undefined') {
    (window as any).__CHAT_HOOK__ = {
      ...(window as any).__CHAT_HOOK__,
      loadUserChatHistory,
    };
  }

  // Ensure messages are loaded for the selected session after both chatSessions and currentSessionId are available
  useEffect(() => {
    if (currentSessionId && chatSessions.length > 0) {
      const session = chatSessions.find(s => s.id === currentSessionId);
      if (session) {
        setMessages(session.messages);
      }
    }
  }, [currentSessionId, chatSessions]);

  // Persist currentSessionId to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('currentSessionId', currentSessionId === null ? 'null' : currentSessionId);
  }, [currentSessionId]);

  const loadChatHistory = () => {
    try {
      const savedSessions = localStorage.getItem('chatSessions');
      if (savedSessions) {
        const sessions = JSON.parse(savedSessions);
        setChatSessions(sessions);
        // Remove auto-selecting most recent session here
      }
    } catch (error) {
      console.error('Error loading chat history:', error);
    }
  };

  const saveChatHistory = useCallback((sessions: ChatSession[]) => {
    try {
      localStorage.setItem('chatSessions', JSON.stringify(sessions));
    } catch (error) {
      console.error('Error saving chat history:', error);
    }
  }, []);

  // Refactored createNewSession: Only clears state, does not add to chatSessions
  const createNewSession = () => {
    setCurrentSessionId(null);
    setMessages([]);
    setAttachedFiles([]);
    // Persist home state
    localStorage.setItem('currentSessionId', 'null');
  };

  // Summarize a file and add the summary as a bot message
  const summarizeFile = async (file: FileAttachment) => {
    setIsLoading(true);
    const userMessage: Message = {
      id: Date.now().toString(),
      content: `Can you summarise this file? (${file.name})`,
      isUser: true,
      timestamp: new Date(),
      files: [file],
    };
    setMessages((prev) => [...prev, userMessage]);
    try {
      const result = file.file ? await fileAPI.summarize(file.file) : 'File not available';
      const summary = result.summary || result.response || result.content || 'No summary returned.';
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: summary,
        isUser: false,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, botMessage]);
    } catch (error: any) {
      const botMessage: Message = {
        id: (Date.now() + 2).toString(),
        content: `Error summarizing file: ${error.message || error}`,
        isUser: false,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, botMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Helper to poll for marker file
  async function waitForMarker(fileName: string, maxAttempts = 30): Promise<boolean> {
    let attempts = 0;
    while (attempts < maxAttempts) {
      const res = await fetch(`/api/status/${encodeURIComponent(fileName)}`);
      const json = await res.json();
      if (json.ready_for_query) return true;
      await new Promise((resolve) => setTimeout(resolve, 2000));
      attempts++;
    }
    return false;
  }

  // Refactored sendMessage: Create and save a new session if none exists
  const sendMessage = async (
    content: string,
    files?: FileAttachment[],
    responseMode?: string,
    sessionId?: string
  ) => {
    if (!content.trim() && (!files || files.length === 0)) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content,
      isUser: true,
      timestamp: new Date(),
      files: files || attachedFiles,
    };

    let currentId = sessionId || currentSessionId;
    let updatedSessions = chatSessions;

    // If no session, create a new one and add to chatSessions
    if (!currentId) {
      const newSession: ChatSession = {
        id: Date.now().toString(),
        title: content.slice(0, 50) + '...',
        messages: [userMessage],
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      updatedSessions = [newSession, ...chatSessions];
      setChatSessions(updatedSessions);
      setCurrentSessionId(newSession.id);
      setMessages([userMessage]);
      setAttachedFiles([]);
      saveChatHistory(updatedSessions);
      currentId = newSession.id;
    } else {
      const updatedMessages = [...messages, userMessage];
      setMessages(updatedMessages);
      setAttachedFiles([]);
      updatedSessions = chatSessions.map(session =>
        session.id === currentId
          ? {
              ...session,
              messages: updatedMessages,
              updatedAt: new Date(),
              title: session.messages.length === 0 ? content.slice(0, 50) + '...' : session.title
            }
          : session
      );
      setChatSessions(updatedSessions);
      saveChatHistory(updatedSessions);
    }

    setIsLoading(true);
    try {
      // If a file is attached, process it first
      if (files && files.length === 1) {
        const file = files[0];
        // Call /api/summarize-document
        const summarizeResp = file.file ? await fileAPI.summarize(file.file) : 'File not available';
        if (summarizeResp.status !== "ready_for_query") {
          // Show 'Processing...' message
          const processingMsg: Message = {
            id: (Date.now() + 1).toString(),
            content: `Processing document...`,
            isUser: false,
            timestamp: new Date(),
          };
          setMessages((prev) => [...prev, processingMsg]);
          // Poll for marker
          const ready = await waitForMarker(file.name);
          // Remove 'Processing...' message
          setMessages((prev) => prev.filter(m => m.id !== processingMsg.id));
          if (!ready) {
            setIsLoading(false);
            setMessages((prev) => [...prev, {
              id: (Date.now() + 2).toString(),
              content: "Error: Document processing timed out. Please try again later.",
              isUser: false,
              timestamp: new Date(),
            }]);
            return;
          }
        }
      }
      // Get user info from localStorage
      let userEmail = '<EMAIL>';
      let userEmployeeId = 'EMP123';
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          const userObj = JSON.parse(userStr);
          if (userObj.email) userEmail = userObj.email;
          if (userObj.employeeId) userEmployeeId = userObj.employeeId;
        }
      } catch (e) {
        // fallback to defaults
      }
      // Call the real backend API with all required fields
      const response = await chatAPI.sendMessage(
        content,
        files?.map(f => f.file).filter((f): f is File => f !== undefined),
        'web-client', // deviceId placeholder
        userEmail, // email from user
        userEmployeeId, // employeeId from user
        currentId, // chatId
        responseMode // response_mode
      );
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        content:
          response && typeof response === 'object' && (
            ('summary' in response && typeof response.summary === 'string' && response.summary) ||
            ('response' in response && typeof response.response === 'string' && response.response) ||
            ('content' in response && typeof response.content === 'string' && response.content)
          ) || "No response from server.",
        isUser: false,
        timestamp: new Date(),
      };
      const finalMessages = [...messages, userMessage, botMessage];
      setMessages(finalMessages);
      // Update current session
      const finalSessions = updatedSessions.map(session =>
        session.id === currentId
          ? {
              ...session,
              messages: finalMessages,
              updatedAt: new Date(),
              title: session.messages.length <= 1 ? content.slice(0, 50) + '...' : session.title
            }
          : session
      );
      setChatSessions(finalSessions);
      saveChatHistory(finalSessions);
    } catch (error: any) {
      // Show error as a bot message
      const botMessage: Message = {
        id: (Date.now() + 2).toString(),
        content: `Error: ${error.message || error}`,
        isUser: false,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, botMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const loadSession = (sessionId: string) => {
    const session = chatSessions.find(s => s.id === sessionId);
    if (session) {
      setCurrentSessionId(sessionId);
      setMessages(session.messages);
      setAttachedFiles([]);
      // Persist selected session
      localStorage.setItem('currentSessionId', sessionId);
    }
  };

  const deleteSession = (sessionId: string) => {
    const updatedSessions = chatSessions.filter(s => s.id !== sessionId);
    setChatSessions(updatedSessions);
    saveChatHistory(updatedSessions);

    if (currentSessionId === sessionId) {
      if (updatedSessions.length > 0) {
        loadSession(updatedSessions[0].id);
      } else {
        createNewSession();
      }
    }
  };

  const archiveSession = (sessionId: string) => {
    const updatedSessions = chatSessions.map(session =>
      session.id === sessionId ? { ...session, isArchived: true } : session
    );
    setChatSessions(updatedSessions);
    saveChatHistory(updatedSessions);
  };

  const addFileAttachment = (file: FileAttachment) => {
    setAttachedFiles(prev => [...prev, file]);
  };

  const removeFileAttachment = (fileId: string) => {
    setAttachedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const clearAllChats = () => {
    setChatSessions([]);
    setMessages([]);
    setCurrentSessionId(null);
    setAttachedFiles([]);
    localStorage.removeItem('chatSessions');
    localStorage.setItem('currentSessionId', 'null');
    createNewSession();
  };

  const renameSession = (sessionId: string, newTitle: string) => {
    const updatedSessions = chatSessions.map(session =>
      session.id === sessionId ? { ...session, title: newTitle, updatedAt: new Date() } : session
    );
    setChatSessions(updatedSessions);
    saveChatHistory(updatedSessions);
  };

  const downloadSession = (sessionId: string) => {
    const session = chatSessions.find(s => s.id === sessionId);
    if (!session) return;
    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(session, null, 2));
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", `${session.title || 'chat'}-${session.id}.json`);
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  };

  const requestClarification = async (selectedText: string, previousMessage: string) => {
    if (!selectedText.trim() || !previousMessage.trim()) return;

    setIsLoading(true);

    try {
      const response = await chatAPI.clarifyMessage(
        selectedText,
        previousMessage,
        undefined, // clarificationQuery - optional
        'web-client', // deviceId
        'en', // language
        'concise' // responseMode
      );

      const clarificationMessage: Message = {
        id: Date.now().toString(),
        content: (response as any).content || 'No response',
        isUser: false,
        timestamp: new Date(),
        sender: 'assistant',
        type: 'clarification',
        metadata: {
          clarification_type: (response as any).metadata?.clarification_type || 'explanation',
          selected_text: selectedText,
          target_phrase: (response as any).target_phrase,
          original_message_preview: (response as any).metadata?.original_message_preview
        }
      };

      // Add to current session
      let currentId = currentSessionId;
      let updatedSessions = chatSessions;

      if (!currentId) {
        const newSession: ChatSession = {
          id: Date.now().toString(),
          title: `Clarification: ${selectedText.slice(0, 30)}...`,
          messages: [clarificationMessage],
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        updatedSessions = [newSession, ...chatSessions];
        setChatSessions(updatedSessions);
        setCurrentSessionId(newSession.id);
        setMessages([clarificationMessage]);
        currentId = newSession.id;
      } else {
        const updatedMessages = [...messages, clarificationMessage];
        setMessages(updatedMessages);

        updatedSessions = chatSessions.map(session =>
          session.id === currentId
            ? { ...session, messages: updatedMessages, updatedAt: new Date() }
            : session
        );
        setChatSessions(updatedSessions);
      }

      saveChatHistory(updatedSessions);
    } catch (error) {
      console.error('Error requesting clarification:', error);
      // Could add error handling UI here
    } finally {
      setIsLoading(false);
    }
  };

  const editMessage = (messageId: string, newContent: string) => {
    const updatedMessages = messages.map(msg =>
      msg.id === messageId
        ? {
            ...msg,
            content: newContent,
            editedAt: new Date(),
            originalContent: msg.originalContent || msg.content
          }
        : msg
    );

    setMessages(updatedMessages);

    // Update the session in chatSessions
    if (currentSessionId) {
      const updatedSessions = chatSessions.map(session =>
        session.id === currentSessionId
          ? {
              ...session,
              messages: updatedMessages,
              updatedAt: new Date(),
            }
          : session
      );
      setChatSessions(updatedSessions);
      saveChatHistory(updatedSessions);
    }
  };

  return {
    messages,
    chatSessions: chatSessions.filter(s => !s.isArchived),
    archivedSessions: chatSessions.filter(s => s.isArchived),
    currentSessionId,
    isLoading,
    attachedFiles,
    sendMessage,
    createNewSession,
    loadSession,
    deleteSession,
    archiveSession,
    addFileAttachment,
    removeFileAttachment,
    clearAllChats,
    renameSession,
    downloadSession,
    summarizeFile, // Expose summarizeFile
    loadUserChatHistory,
    requestClarification,
    editMessage,
  };
};
