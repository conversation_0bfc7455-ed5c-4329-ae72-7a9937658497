import React, { useState, useEffect } from 'react';
import { useSpeechRecognition } from '@/hooks/useSpeechRecognition';

interface VoiceModalProps {
  onClose: () => void;
  onSubmitTranscript: (transcript: string) => void;
}

const VoiceModal: React.FC<VoiceModalProps> = ({ onClose, onSubmitTranscript }) => {
  const {
    isListening,
    isSupported,
    transcript,
    error,
    startListening,
    stopListening,
    resetTranscript,
  } = useSpeechRecognition();

  const [editableTranscript, setEditableTranscript] = useState('');

  useEffect(() => {
    setEditableTranscript(transcript);
  }, [transcript]);

  const handleStartRecording = () => {
    resetTranscript();
    setEditableTranscript('');
    startListening();
  };

  const handleStopRecording = () => {
    stopListening();
  };

  const handleSubmit = () => {
    if (editableTranscript.trim()) {
      onSubmitTranscript(editableTranscript.trim());
    }
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isSupported) {
    return (
      <div className="modal-overlay" onClick={handleOverlayClick}>
        <div className="modal-content">
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Voice Input
            </h3>
            <button onClick={onClose} className="icon-btn">
              <i className="fas fa-times text-gray-500 dark:text-gray-400"></i>
            </button>
          </div>
          <div className="p-6 text-center">
            <p className="text-gray-600 dark:text-gray-400">
              Speech recognition is not supported in your browser.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="modal-overlay" onClick={handleOverlayClick}>
      <div className="modal-content">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Voice Input
          </h3>
          <button onClick={onClose} className="icon-btn">
            <i className="fas fa-times text-gray-500 dark:text-gray-400"></i>
          </button>
        </div>

        {/* Body */}
        <div className="p-6">
          {/* Recording Indicator */}
          <div className="text-center mb-6">
            <div className="flex justify-center mb-4">
              {isListening && (
                <div className="flex space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <div
                      key={i}
                      className="w-1 bg-blue-500 dark:bg-blue-400 rounded-full animate-pulse"
                      style={{
                        height: `${20 + Math.random() * 20}px`,
                        animationDelay: `${i * 0.1}s`,
                      }}
                    />
                  ))}
                </div>
              )}
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              {isListening ? 'Listening...' : 'Click start to begin recording'}
            </p>
          </div>

          {/* Recording Controls */}
          <div className="flex justify-center space-x-4 mb-6">
            <button
              onClick={handleStartRecording}
              disabled={isListening}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z" fill="currentColor"/>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12 19v4M8 23h8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              <span>Start</span>
            </button>
            
            <button
              onClick={handleStopRecording}
              disabled={!isListening}
              className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <i className="fas fa-stop"></i>
              <span>Stop</span>
            </button>
          </div>

          {/* Transcription Result */}
          <div className="mb-4">
            <label className="form-label">
              Transcription (you can edit this before submitting):
            </label>
            <textarea
              value={editableTranscript}
              onChange={(e) => setEditableTranscript(e.target.value)}
              className="form-input"
              rows={4}
              placeholder="Your speech will appear here..."
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              You can edit the text before submitting.
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
              <p className="text-sm text-red-600 dark:text-red-400">
                Error: {error}
              </p>
            </div>
          )}

          {/* Footer Buttons */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              disabled={!editableTranscript.trim()}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Submit
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VoiceModal;
