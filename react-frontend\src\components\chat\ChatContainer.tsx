import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Message, FileAttachment } from '@/types';
import ChatMessages from './ChatMessages';
import EnhancedChatInputBox from './EnhancedChatInputBox';
import HRSuggestionButtons from './HRSuggestionButtons';
import SelectionActionButton from './SelectionActionButton';
import { cn } from '@/lib/utils';
import Logo from '/img/favicon.png';
import { useChat } from '@/hooks/useChat';
import { useRef, useState, useEffect } from 'react';
import { ArrowDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import EscalationModal from '../modals/EscalationModal';
import { useAuth } from '@/hooks/useAuth';
import { escalationAPI } from '../../utils/api';

interface ChatContainerProps {
  messages: Message[];
  isLoading: boolean;
  attachedFiles: FileAttachment[];
  onSendMessage: (message: string, files?: FileAttachment[], responseMode?: string) => void;
  onAddFile: (file: FileAttachment) => void;
  onRemoveFile: (fileId: string) => void;
  onOpenEscalation: () => void;
  summarizeFile?: (file: FileAttachment) => Promise<string>;
  className?: string;
  user?: any;
  onUpdateUser?: (updates: any) => void;
  onClarificationRequest?: (selectedText: string, previousMessage: string) => void;
  onEditMessage?: (messageId: string, newContent: string) => void;
}

const ChatContainer: React.FC<ChatContainerProps> = ({
  messages,
  isLoading,
  attachedFiles,
  onSendMessage,
  onAddFile,
  onRemoveFile,
  onOpenEscalation,
  summarizeFile,
  className,
  user,
  onUpdateUser,
  onClarificationRequest,
  onEditMessage,
}) => {
  // Ref to scroll to bottom
  const chatBottomRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [showEscalation, setShowEscalation] = useState(false);
  const [showDropdownMenu, setShowDropdownMenu] = useState(true);
  const [attachedText, setAttachedText] = useState<string>('');
  const handleOpenEscalation = () => {
    setShowDropdownMenu(false);
    setShowEscalation(true);
  };
  const handleCloseEscalation = () => {
    setShowDropdownMenu(true);
    setShowEscalation(false);
  };

  const handleScrollToBottom = () => {
    chatBottomRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleScroll = () => {
    if (!messagesContainerRef.current || !chatBottomRef.current) return;

    const container = messagesContainerRef.current;
    // Check if user is at the bottom (within 1mm tolerance, approximately 3-4 pixels)
    const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 4;

    // Show button only when content overflows and user is not at bottom
    const hasOverflow = container.scrollHeight > container.clientHeight;
    setShowScrollButton(!isAtBottom && hasOverflow && messages.length > 0);
  };

  useEffect(() => {
    const container = messagesContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, []);

  const handleRemoveAttachedText = () => {
    setAttachedText('');
  };

  // Direct message sending from selection prompts
  const handleSendMessageFromSelection = (message: string) => {
    onSendMessage(message);
  };

  return (
    <div className={cn("flex flex-col h-full w-full bg-gray-50 dark:bg-gray-900 overflow-hidden", className)}>
      {/* Selection Action Button */}
      <SelectionActionButton onSendMessage={handleSendMessageFromSelection} />
      
      {messages.length === 0 ? (
        <div className="w-full max-w-4xl mx-auto mt-4 px-4 py-6"> {/* align content towards top with margin */}
          {/* Welcome Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="flex flex-col items-center justify-center text-center mb-8"
          >
            <img src={Logo} alt="Company Logo" className="w-16 h-16 rounded-3xl mb-4 shadow-md" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Welcome to Proxima28</h1>
            <p className="text-base text-gray-600 max-w-2xl">
              Your intelligent HR assistant. Ask me anything about company policies, benefits, or procedures.
            </p>
          </motion.div>

          {/* HR Suggestion Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="w-full max-w-5xl mx-auto mb-8"
          >
            <HRSuggestionButtons onSuggestionClick={onSendMessage} />
          </motion.div>

          {/* Chat Input Box - positioned below suggestions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="w-full max-w-3xl mx-auto mt-8 px-4 sm:px-0"
          >
            <EnhancedChatInputBox
              onSendMessage={onSendMessage}
              attachedFiles={attachedFiles}
              onAddFile={onAddFile}
              onRemoveFile={onRemoveFile}
              isLoading={isLoading}
              mode="welcome"
              placeholder="Ask anything or @mention a Space"
              onOpenEscalation={handleOpenEscalation}
              showDropdownMenu={showDropdownMenu}
              setShowDropdownMenu={setShowDropdownMenu}
            />
          </motion.div>
        </div>
      ) : (
        <div className="flex flex-col h-full w-full">
          <div className="flex-1 w-full overflow-y-auto" ref={messagesContainerRef} onScroll={handleScroll}>
            <div className="w-full max-w-4xl px-4 sm:px-6 md:px-8 mx-auto py-4">
              <ChatMessages
                messages={messages}
                isLoading={isLoading}
                onSuggestionClick={onSendMessage}
                bottomRef={chatBottomRef}
                onClarificationRequest={onClarificationRequest}
                onEditMessage={onEditMessage}
              />
            </div>
          </div>
          {/* Chat Input at the bottom, ChatGPT style */}
          <div className="w-full flex justify-center py-4 px-4" style={{ position: 'relative' }}>
            {/* Scroll to bottom button - float above input box */}
            <AnimatePresence>
              {showScrollButton && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="w-full flex justify-center"
                  style={{ position: 'absolute', left: 0, right: 0, top: '-22px', zIndex: 30, pointerEvents: 'none' }}
                >
                  <div style={{ pointerEvents: 'auto' }}>
                    <Button
                      variant="secondary"
                      size="icon"
                      onClick={handleScrollToBottom}
                      className="rounded-full shadow-lg hover:shadow-xl transition-all bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700"
                      style={{ width: 29, height: 29, minWidth: 29, minHeight: 29, position: 'relative', zIndex: 30 }}
                      aria-label="Scroll to bottom"
                    >
                      <ArrowDown className="h-4 w-4" />
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
            <div className="w-full" style={{ maxWidth: 'calc(56rem - 4cm)' }}>
              <EnhancedChatInputBox
                onSendMessage={onSendMessage}
                attachedFiles={attachedFiles}
                onAddFile={onAddFile}
                onRemoveFile={onRemoveFile}
                isLoading={isLoading}
                mode="chat"
                placeholder="Ask anything about leaves, benefits, or company policies..."
                onOpenEscalation={handleOpenEscalation}
                showDropdownMenu={showDropdownMenu}
                setShowDropdownMenu={setShowDropdownMenu}
              />
            </div>
          </div>
        </div>
      )}
      {showEscalation && (
        <EscalationModal
          onClose={handleCloseEscalation}
          user={user}
          onSubmit={async (formData) => {
            await escalationAPI.submit(formData);
            // Optionally handle confirmation/close here
          }}
        />
      )}
    </div>
  );
};

export default ChatContainer;
