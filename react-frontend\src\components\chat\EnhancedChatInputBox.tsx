import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useHotkeys } from 'react-hotkeys-hook';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { FileAttachment } from '@/types';
import { useSpeechRecognition } from '@/hooks/useSpeechRecognition';
import { 
  Paperclip, 
  Send, 
  Mic, 
  MicOff, 
  Square,
  Loader2,
  X
} from 'lucide-react';

interface EnhancedChatInputBoxProps {
  onSendMessage: (message: string, files?: FileAttachment[], responseMode?: string) => void;
  attachedFiles?: FileAttachment[];
  onAddFile?: (file: FileAttachment) => void;
  onRemoveFile?: (fileId: string) => void;
  isLoading?: boolean;
  placeholder?: string;
  mode?: 'welcome' | 'chat';
  className?: string;
  onOpenEscalation?: () => void;
  showDropdownMenu?: boolean;
  setShowDropdownMenu?: (show: boolean) => void;
}

const EnhancedChatInputBox: React.FC<EnhancedChatInputBoxProps> = ({
  onSendMessage,
  attachedFiles = [],
  onAddFile,
  onRemoveFile,
  isLoading = false,
  placeholder = "Ask anything or @mention a Space",
  mode = 'welcome',
  className = '',
  onOpenEscalation,
  showDropdownMenu = true,
  setShowDropdownMenu
}) => {
  const [message, setMessage] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const [responseMode, setResponseMode] = useState<'concise' | 'detailed'>('detailed');
  const [isFocused, setIsFocused] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Speech recognition hook
  const {
    isListening,
    isSupported,
    transcript,
    error,
    startListening,
    stopListening,
    resetTranscript,
  } = useSpeechRecognition();

  // Update message when transcript changes
  useEffect(() => {
    if (transcript && isRecording) {
      setMessage(transcript);
    }
  }, [transcript, isRecording]);

  // Keyboard shortcuts
  useHotkeys('cmd+enter,ctrl+enter', () => handleSubmit(), {
    enableOnFormTags: ['textarea'],
  });

  useHotkeys('cmd+k,ctrl+k', (e) => {
    e.preventDefault();
    fileInputRef.current?.click();
  });

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  // Reset input when mode changes to welcome (New Chat)
  useEffect(() => {
    if (mode === 'welcome') {
      setMessage('');
      if (textareaRef.current) {
        (textareaRef.current as HTMLTextAreaElement).style.height = 'auto';
      }
    }
  }, [mode]);

  const handleSubmit = (e?: React.FormEvent) => {
    e?.preventDefault();
    if (message.trim() && !isLoading && !isComposing) {
      onSendMessage(message.trim(), attachedFiles, responseMode);
      setMessage('');
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    files.forEach((file) => {
      if (onAddFile) {
        const fileAttachment: FileAttachment = {
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          name: file.name,
          type: file.type,
          size: file.size,
          file: file,
        };
        onAddFile(fileAttachment);
      }
    });
    e.target.value = '';
  };

  const handleVoiceToggle = () => {
    if (!isSupported) {
      alert('Speech recognition is not supported in your browser.');
      return;
    }

    if (isListening) {
      stopListening();
      setIsRecording(false);
    } else {
      resetTranscript();
      setMessage(''); // Clear existing message
      startListening();
      setIsRecording(true);
    }
  };

  const handleStopRecording = () => {
    stopListening();
    setIsRecording(false);
  };

  const canSend = message.trim().length > 0 && !isLoading && !isComposing;

  return (
    <div className={cn("w-full", className)}>
      {/* Attached Files */}
      <AnimatePresence>
        {attachedFiles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-3 flex flex-wrap gap-2"
          >
            {attachedFiles.map((file) => (
              <motion.div
                key={file.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center gap-2 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-3 py-1.5 rounded-lg text-sm border border-blue-200 dark:border-blue-800"
              >
                <Paperclip className="w-3 h-3" />
                <span className="truncate max-w-[150px]">{file.name}</span>
                {onRemoveFile && (
                  <button
                    onClick={() => onRemoveFile(file.id)}
                    className="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-200"
                  >
                    <X className="w-3 h-3" />
                  </button>
                )}
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Voice Recording Indicator */}
      <AnimatePresence>
        {isRecording && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mb-3 flex items-center gap-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 px-3 py-2 rounded-lg text-sm border border-red-200 dark:border-red-800"
          >
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
              <span>Listening... Speak now</span>
            </div>
            <button
              onClick={handleStopRecording}
              className="ml-auto text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-200"
            >
              <Square className="w-4 h-4" />
            </button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Error Message */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mb-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 px-3 py-2 rounded-lg text-sm border border-red-200 dark:border-red-800"
          >
            Voice recognition error: {error}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Input Container */}
      <form onSubmit={handleSubmit} className="relative">
        <div className={cn(
          "relative flex items-end gap-2 bg-white dark:bg-gray-800 border rounded-2xl transition-all duration-200",
          isFocused 
            ? "border-blue-500 dark:border-blue-400 shadow-lg shadow-blue-500/10" 
            : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500",
          mode === 'welcome' ? "shadow-lg" : "shadow-sm"
        )}>
          {/* File Input */}
          <input
            ref={fileInputRef}
            type="file"
            multiple
            onChange={handleFileSelect}
            className="hidden"
            accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg,.gif"
          />

          {/* Left Icons */}
          <div className="flex items-center gap-1 ml-3 mb-2">
            {onAddFile && (
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                title="Attach file"
              >
                <Paperclip className="w-4 h-4 text-gray-500 dark:text-gray-400" />
              </button>
            )}
          </div>

          {/* Text Input */}
          <div className="flex-1 min-w-0">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              onCompositionStart={() => setIsComposing(true)}
              onCompositionEnd={() => setIsComposing(false)}
              placeholder={isRecording ? "Listening..." : placeholder}
              className="w-full resize-none border-0 bg-transparent py-3 px-0 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-0 text-base leading-6"
              rows={1}
              style={{ minHeight: '24px', maxHeight: '200px' }}
              disabled={isRecording}
            />
          </div>

          {/* Right Icons */}
          <div className="flex items-center gap-2 mr-3 mb-2">
            {/* Voice Button */}
            <button
              type="button"
              onClick={handleVoiceToggle}
              className={cn(
                "p-1.5 rounded-lg transition-all duration-200",
                isRecording
                  ? "bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-900/50"
                  : "hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400"
              )}
              title={isRecording ? "Stop recording" : "Voice input"}
            >
              {isRecording ? (
                <MicOff className="w-4 h-4" />
              ) : (
                <Mic className="w-4 h-4" />
              )}
            </button>

            {/* Send Button */}
            <button
              type="submit"
              disabled={!canSend}
              className={cn(
                "p-1.5 rounded-lg transition-all duration-200",
                canSend
                  ? "bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
                  : "bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed"
              )}
              title="Send message"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default EnhancedChatInputBox;
