import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Header from './components/layout/Header';
import Sidebar from './components/layout/Sidebar';
import ChatContainer from './components/chat/ChatContainer';

import { Message, FileAttachment, ChatSession, User } from './types';
import { useChat } from './hooks/useChat';
import { useAuth } from './hooks/useAuth';
import { cn } from './lib/utils';

const App: React.FC = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [showHRTeam, setShowHRTeam] = useState(false);

  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);

  const { user, logout } = useAuth();
  const {
    messages,
    isLoading,
    attachedFiles,
    sendMessage,
    addFileAttachment: addFile,
    removeFileAttachment: removeFile,
    summarizeFile,
    requestClarification: clarificationRequest,
    editMessage
  } = useChat();

  const handleToggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  const handleNewChat = () => {
    const newSessionId = `session_${Date.now()}`;
    const newSession: ChatSession = {
      id: newSessionId,
      title: 'New Chat',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      messageCount: 0
    };
    
    setChatSessions(prev => [newSession, ...prev]);
    setCurrentSessionId(newSessionId);
  };

  const handleLoadSession = (sessionId: string) => {
    setCurrentSessionId(sessionId);
  };

  const handleDeleteSession = (sessionId: string) => {
    setChatSessions(prev => prev.filter(session => session.id !== sessionId));
    if (currentSessionId === sessionId) {
      setCurrentSessionId(null);
    }
  };

  const handleArchiveSession = (sessionId: string) => {
    // Implementation for archiving session
    console.log('Archive session:', sessionId);
  };

  const handleRenameSession = (sessionId: string, newTitle: string) => {
    setChatSessions(prev => 
      prev.map(session => 
        session.id === sessionId 
          ? { ...session, title: newTitle, updatedAt: new Date().toISOString() }
          : session
      )
    );
  };

  const handleDownloadSession = (sessionId: string) => {
    // Implementation for downloading session
    console.log('Download session:', sessionId);
  };

  const handleOpenSearch = () => {
    setShowSearch(true);
  };

  const handleShowHRTeam = () => {
    setShowHRTeam(true);
  };

  const handleOpenSettings = () => {
    setShowSettings(true);
  };

  const handleLogout = () => {
    logout();
  };

  const handleUpdateUser = (updates: any) => {
    // Implementation for updating user
    console.log('Update user:', updates);
  };

  // Mock functions for features not yet implemented

  const handleOpenEscalation = () => {
    console.log('Escalation feature not yet implemented');
  };

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      {/* Sidebar */}
      <Sidebar
        isCollapsed={isSidebarCollapsed}
        onToggle={handleToggleSidebar}
        chatSessions={chatSessions}
        currentSessionId={currentSessionId}
        onNewChat={handleNewChat}
        onLoadSession={handleLoadSession}
        onDeleteSession={handleDeleteSession}
        onArchiveSession={handleArchiveSession}
        onRenameSession={handleRenameSession}
        onDownloadSession={handleDownloadSession}
        onOpenSearch={handleOpenSearch}
        onShowHRTeam={handleShowHRTeam}
      />

      {/* Main Content */}
      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Header */}
        <Header
          user={user}
          onOpenSettings={handleOpenSettings}
          onLogout={handleLogout}
        />

        {/* Chat Container */}
        <div className="flex-1 overflow-hidden">
          <ChatContainer
            messages={messages}
            isLoading={isLoading}
            attachedFiles={attachedFiles}
            onSendMessage={sendMessage}
            onAddFile={addFile}
            onRemoveFile={removeFile}

            onOpenEscalation={handleOpenEscalation}
            summarizeFile={summarizeFile}
            user={user}
            onUpdateUser={handleUpdateUser}
            onClarificationRequest={clarificationRequest}
            onEditMessage={editMessage}
          />
        </div>
      </div>


    </div>
  );
};

export default App; 