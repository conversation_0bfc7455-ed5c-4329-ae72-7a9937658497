# Proxima28 Chatbot - React Frontend

This is the migrated React + Tailwind CSS frontend for the Zillaq chatbot application, converted from the legacy HTML/CSS/JavaScript implementation.

## 🚀 Features

- **Modern React Architecture**: Component-based structure with TypeScript
- **Tailwind CSS Styling**: Utility-first CSS framework with custom design system
- **Responsive Design**: Mobile-first approach with responsive layouts
- **Dark/Light Theme**: System preference detection with manual override
- **Authentication**: Login/Register with 2FA support
- **Chat Interface**: Real-time messaging with file attachments
- **Voice Input**: Speech recognition integration
- **HR Escalation**: Built-in escalation form for HR issues
- **Settings Management**: User preferences and account management
- **Chat History**: Session management with archive functionality

## 📁 Project Structure

```
src/
├── components/
│   ├── auth/           # Authentication components
│   ├── chat/           # Chat interface components
│   ├── layout/         # Layout components (Header, Sidebar)
│   └── modals/         # Modal dialogs
├── hooks/              # Custom React hooks
├── types/              # TypeScript type definitions
├── styles/             # Global styles and Tailwind config
└── utils/              # Utility functions
```

## 🛠️ Installation & Setup

1. **Install Dependencies**
   ```bash
   cd react-frontend
   npm install
   ```

2. **Copy Assets**
   ```bash
   # Copy favicon from legacy static folder
   cp ../static/img/favicon.png public/favicon.png
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

4. **Build for Production**
   ```bash
   npm run build
   ```

## 🎨 Design System

The application uses a comprehensive design system built with Tailwind CSS:

### Color Palette
- **Light Theme**: Black/white/gray palette for maximum readability
- **Dark Theme**: Dark backgrounds with light text for reduced eye strain
- **Accent Colors**: Consistent accent colors for interactive elements

### Typography
- **Font Family**: Inter, Segoe UI, Roboto (system fonts)
- **Font Weights**: Regular (400) for body text, medium (500) for labels, semibold (600) for headings

### Components
- **Buttons**: Primary, secondary, and icon button variants
- **Forms**: Consistent input styling with focus states
- **Modals**: Centered overlays with backdrop blur
- **Cards**: Subtle shadows and rounded corners

## 🔧 Key Components

### Layout Components
- **Header**: Brand, user account, new chat button
- **Sidebar**: Navigation, chat history, file upload
- **ChatContainer**: Main chat interface with messages and input

### Chat Components
- **ChatMessages**: Message display with loading states
- **MessageBubble**: Individual message styling with timestamps
- **EnhancedChatInputBox**: Input area with file upload, inline voice input, and send
- **SelectionActionButton**: "Ask Proxima" functionality for selected text
- **SuggestionChips**: Quick action buttons

### Modal Components
- **LoginModal**: Authentication with password toggle
- **RegisterModal**: User registration form
- **SettingsModal**: Tabbed settings interface
- **EscalationModal**: HR escalation form

### Custom Hooks
- **useAuth**: Authentication state management
- **useChat**: Chat functionality and history
- **useTheme**: Theme switching and persistence
- **useSpeechRecognition**: Voice input handling

## 🔄 Migration Notes

This React frontend preserves all functionality from the legacy implementation:

### Preserved Features
- ✅ Exact visual design and layout
- ✅ Dark/light theme switching
- ✅ Authentication flow with 2FA
- ✅ Chat interface with file uploads
- ✅ Voice recognition
- ✅ HR escalation form
- ✅ Settings management
- ✅ Chat history and archiving
- ✅ Responsive design

### Improvements
- 🚀 Modern React architecture
- 🎨 Tailwind CSS utility classes
- 📱 Better mobile responsiveness
- ⚡ Improved performance
- 🔧 TypeScript type safety
- 🧪 Component-based testing

## 🌐 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 📝 Development Guidelines

### Code Style
- Use TypeScript for type safety
- Follow React best practices
- Use Tailwind utilities over custom CSS
- Implement proper error boundaries
- Add loading states for async operations

### Component Structure
- Keep components small and focused
- Use custom hooks for complex logic
- Implement proper prop types
- Add accessibility attributes
- Handle edge cases gracefully

### Styling Guidelines
- Use Tailwind utility classes
- Follow the established design system
- Maintain consistent spacing
- Ensure proper contrast ratios
- Test in both light and dark themes

## 🔗 Integration

To integrate with the backend:

1. Update API endpoints in `src/utils/api.ts`
2. Modify authentication logic in `src/hooks/useAuth.ts`
3. Update chat functionality in `src/hooks/useChat.ts`
4. Configure file upload handling
5. Set up proper error handling

## 📄 License

This project maintains the same license as the original application.
